<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1751484075459" clover="3.2.0">
  <project timestamp="1751484075459" name="All files">
    <metrics statements="1202" coveredstatements="362" conditionals="380" coveredconditionals="94" methods="240" coveredmethods="72" elements="1822" coveredelements="528" complexity="0" loc="1202" ncloc="1202" packages="17" files="49" classes="49"/>
    <package name="src">
      <metrics statements="8" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="middleware.ts" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/middleware.ts">
        <metrics statements="8" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.__tests__.mocks">
      <metrics statements="67" coveredstatements="11" conditionals="11" coveredconditionals="0" methods="22" coveredmethods="0"/>
      <file name="data.ts" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/mocks/data.ts">
        <metrics statements="11" coveredstatements="11" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="5" type="stmt"/>
        <line num="19" count="15" type="stmt"/>
        <line num="22" count="5" type="stmt"/>
        <line num="76" count="31" type="stmt"/>
        <line num="87" count="5" type="stmt"/>
        <line num="118" count="5" type="stmt"/>
        <line num="145" count="5" type="stmt"/>
        <line num="192" count="6" type="stmt"/>
        <line num="225" count="14" type="stmt"/>
        <line num="248" count="5" type="stmt"/>
        <line num="261" count="5" type="stmt"/>
      </file>
      <file name="handlers.ts" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/mocks/handlers.ts">
        <metrics statements="29" coveredstatements="0" conditionals="7" coveredconditionals="0" methods="13" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
      </file>
      <file name="providers.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/mocks/providers.tsx">
        <metrics statements="18" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="44" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="58" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="79" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="107" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
      </file>
      <file name="server.ts" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/mocks/server.ts">
        <metrics statements="9" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.__tests__.utils">
      <metrics statements="42" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="19" coveredmethods="0"/>
      <file name="test-utils.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/utils/test-utils.tsx">
        <metrics statements="42" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="19" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="114" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app">
      <metrics statements="17" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <file name="layout.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/app/layout.tsx">
        <metrics statements="5" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
      </file>
      <file name="page.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/app/page.tsx">
        <metrics statements="12" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.(main)">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="layout.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/layout.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.(main).about">
      <metrics statements="5" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="page.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/about/page.tsx">
        <metrics statements="5" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.(main).calendar">
      <metrics statements="131" coveredstatements="0" conditionals="84" coveredconditionals="0" methods="23" coveredmethods="0"/>
      <file name="page.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/calendar/page.tsx">
        <metrics statements="131" coveredstatements="0" conditionals="84" coveredconditionals="0" methods="23" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="172" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="230" count="0" type="stmt"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="243" count="0" type="stmt"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="253" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="298" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="404" count="0" type="stmt"/>
        <line num="414" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="415" count="0" type="stmt"/>
        <line num="416" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="486" count="0" type="stmt"/>
        <line num="487" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.(main).changelogs">
      <metrics statements="12" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="5" coveredmethods="0"/>
      <file name="page.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/changelogs/page.tsx">
        <metrics statements="12" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.app.login">
      <metrics statements="32" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="page.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/app/login/page.tsx">
        <metrics statements="32" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="64" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="71" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="Header.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/Header.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.layout">
      <metrics statements="36" coveredstatements="0" conditionals="19" coveredconditionals="0" methods="8" coveredmethods="0"/>
      <file name="AppLayout.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/layout/AppLayout.tsx">
        <metrics statements="20" coveredstatements="0" conditionals="13" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="29" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="1"/>
      </file>
      <file name="Footer.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/layout/Footer.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
      </file>
      <file name="Header.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/layout/Header.tsx">
        <metrics statements="15" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.ui">
      <metrics statements="245" coveredstatements="19" conditionals="46" coveredconditionals="6" methods="36" coveredmethods="4"/>
      <file name="alert.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/alert.tsx">
        <metrics statements="8" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
      </file>
      <file name="badge.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/badge.tsx">
        <metrics statements="5" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
      </file>
      <file name="button.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/button.tsx">
        <metrics statements="9" coveredstatements="9" conditionals="3" coveredconditionals="3" methods="1" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="41" count="24" type="cond" truecount="2" falsecount="0"/>
        <line num="47" count="1" type="stmt"/>
        <line num="49" count="25" type="stmt"/>
      </file>
      <file name="card.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/card.tsx">
        <metrics statements="9" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
      </file>
      <file name="dialog.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/dialog.tsx">
        <metrics statements="24" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
      </file>
      <file name="empty-state.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/empty-state.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
      </file>
      <file name="error-boundary.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/error-boundary.tsx">
        <metrics statements="25" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="39" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
      </file>
      <file name="form.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/form.tsx">
        <metrics statements="44" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="151" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
      </file>
      <file name="input.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/input.tsx">
        <metrics statements="5" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="24" count="34" type="stmt"/>
      </file>
      <file name="label.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/label.tsx">
        <metrics statements="7" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
      </file>
      <file name="lazy-image.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/lazy-image.tsx">
        <metrics statements="21" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
      </file>
      <file name="loading-spinner.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/loading-spinner.tsx">
        <metrics statements="5" coveredstatements="5" conditionals="3" coveredconditionals="3" methods="2" coveredmethods="2"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="16" count="11" type="cond" truecount="1" falsecount="0"/>
        <line num="25" count="9" type="cond" truecount="1" falsecount="0"/>
      </file>
      <file name="select.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/select.tsx">
        <metrics statements="24" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
      </file>
      <file name="separator.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/separator.tsx">
        <metrics statements="5" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
      </file>
      <file name="skeleton.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/skeleton.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
      </file>
      <file name="skip-to-content.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/skip-to-content.tsx">
        <metrics statements="7" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="table.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/table.tsx">
        <metrics statements="11" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
      </file>
      <file name="textarea.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/textarea.tsx">
        <metrics statements="5" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
      </file>
      <file name="toast.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/toast.tsx">
        <metrics statements="21" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
      </file>
      <file name="toaster.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/toaster.tsx">
        <metrics statements="5" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.contexts">
      <metrics statements="50" coveredstatements="49" conditionals="28" coveredconditionals="27" methods="17" coveredmethods="17"/>
      <file name="AppContext.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/contexts/AppContext.tsx">
        <metrics statements="50" coveredstatements="49" conditionals="28" coveredconditionals="27" methods="17" coveredmethods="17"/>
        <line num="3" count="5" type="stmt"/>
        <line num="5" count="5" type="stmt"/>
        <line num="29" count="5" type="stmt"/>
        <line num="47" count="30" type="cond" truecount="10" falsecount="1"/>
        <line num="49" count="2" type="stmt"/>
        <line num="59" count="5" type="stmt"/>
        <line num="70" count="2" type="stmt"/>
        <line num="81" count="2" type="stmt"/>
        <line num="94" count="3" type="stmt"/>
        <line num="100" count="2" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="115" count="2" type="stmt"/>
        <line num="124" count="2" type="stmt"/>
        <line num="133" count="9" type="stmt"/>
        <line num="134" count="9" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="152" count="5" type="stmt"/>
        <line num="158" count="29" type="stmt"/>
        <line num="159" count="56" type="stmt"/>
        <line num="162" count="56" type="stmt"/>
        <line num="163" count="28" type="stmt"/>
        <line num="164" count="28" type="cond" truecount="1" falsecount="0"/>
        <line num="165" count="8" type="stmt"/>
        <line num="170" count="56" type="stmt"/>
        <line num="171" count="40" type="cond" truecount="3" falsecount="0"/>
        <line num="172" count="4" type="stmt"/>
        <line num="177" count="4" type="stmt"/>
        <line num="185" count="23" type="stmt"/>
        <line num="186" count="46" type="stmt"/>
        <line num="187" count="46" type="cond" truecount="1" falsecount="0"/>
        <line num="188" count="4" type="stmt"/>
        <line num="190" count="42" type="stmt"/>
        <line num="194" count="11" type="stmt"/>
        <line num="195" count="10" type="stmt"/>
        <line num="196" count="10" type="stmt"/>
        <line num="199" count="2" type="stmt"/>
        <line num="200" count="1" type="stmt"/>
        <line num="201" count="1" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="206" count="6" type="stmt"/>
        <line num="207" count="5" type="stmt"/>
        <line num="208" count="5" type="stmt"/>
        <line num="211" count="1" type="stmt"/>
        <line num="212" count="1" type="stmt"/>
        <line num="216" count="10" type="stmt"/>
        <line num="217" count="9" type="stmt"/>
        <line num="218" count="9" type="stmt"/>
        <line num="220" count="1" type="stmt"/>
        <line num="221" count="2" type="stmt"/>
        <line num="222" count="2" type="stmt"/>
      </file>
    </package>
    <package name="src.hooks">
      <metrics statements="159" coveredstatements="148" conditionals="21" coveredconditionals="12" methods="29" coveredmethods="23"/>
      <file name="use-calendar-data.ts" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/hooks/use-calendar-data.ts">
        <metrics statements="94" coveredstatements="94" conditionals="5" coveredconditionals="3" methods="6" coveredmethods="6"/>
        <line num="1" count="3" type="stmt"/>
        <line num="2" count="3" type="stmt"/>
        <line num="3" count="3" type="stmt"/>
        <line num="4" count="3" type="stmt"/>
        <line num="14" count="3" type="stmt"/>
        <line num="15" count="3" type="stmt"/>
        <line num="16" count="3" type="stmt"/>
        <line num="18" count="19" type="stmt"/>
        <line num="19" count="17" type="stmt"/>
        <line num="20" count="17" type="stmt"/>
        <line num="21" count="17" type="stmt"/>
        <line num="22" count="17" type="stmt"/>
        <line num="24" count="17" type="stmt"/>
        <line num="26" count="3" type="stmt"/>
        <line num="28" count="3" type="stmt"/>
        <line num="29" count="3" type="stmt"/>
        <line num="30" count="2" type="stmt"/>
        <line num="31" count="2" type="stmt"/>
        <line num="32" count="2" type="stmt"/>
        <line num="33" count="2" type="stmt"/>
        <line num="34" count="2" type="stmt"/>
        <line num="36" count="2" type="stmt"/>
        <line num="41" count="2" type="stmt"/>
        <line num="49" count="2" type="stmt"/>
        <line num="50" count="2" type="stmt"/>
        <line num="51" count="2" type="stmt"/>
        <line num="53" count="2" type="stmt"/>
        <line num="54" count="2" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="67" count="17" type="stmt"/>
        <line num="69" count="2" type="stmt"/>
        <line num="71" count="2" type="stmt"/>
        <line num="72" count="2" type="stmt"/>
        <line num="73" count="2" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="108" count="17" type="stmt"/>
        <line num="113" count="3" type="stmt"/>
        <line num="114" count="3" type="stmt"/>
        <line num="116" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="117" count="1" type="stmt"/>
        <line num="120" count="2" type="stmt"/>
        <line num="122" count="2" type="stmt"/>
        <line num="123" count="2" type="stmt"/>
        <line num="125" count="2" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="151" count="2" type="stmt"/>
        <line num="157" count="17" type="stmt"/>
        <line num="159" count="2" type="stmt"/>
        <line num="160" count="2" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="165" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
        <line num="173" count="17" type="stmt"/>
        <line num="174" count="1" type="stmt"/>
        <line num="175" count="1" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="179" count="17" type="stmt"/>
      </file>
      <file name="use-notifications.ts" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/hooks/use-notifications.ts">
        <metrics statements="12" coveredstatements="12" conditionals="0" coveredconditionals="0" methods="5" coveredmethods="5"/>
        <line num="1" count="4" type="stmt"/>
        <line num="3" count="18" type="stmt"/>
        <line num="4" count="17" type="stmt"/>
        <line num="6" count="17" type="stmt"/>
        <line num="7" count="6" type="stmt"/>
        <line num="14" count="17" type="stmt"/>
        <line num="15" count="3" type="stmt"/>
        <line num="22" count="17" type="stmt"/>
        <line num="23" count="3" type="stmt"/>
        <line num="30" count="17" type="stmt"/>
        <line num="31" count="3" type="stmt"/>
        <line num="38" count="17" type="stmt"/>
      </file>
      <file name="use-toast.ts" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/hooks/use-toast.ts">
        <metrics statements="53" coveredstatements="42" conditionals="16" coveredconditionals="9" methods="18" coveredmethods="12"/>
        <line num="4" count="4" type="stmt"/>
        <line num="11" count="4" type="stmt"/>
        <line num="12" count="4" type="stmt"/>
        <line num="21" count="4" type="stmt"/>
        <line num="28" count="4" type="stmt"/>
        <line num="31" count="21" type="stmt"/>
        <line num="32" count="21" type="stmt"/>
        <line num="59" count="4" type="stmt"/>
        <line num="61" count="4" type="stmt"/>
        <line num="62" count="4" type="cond" truecount="0" falsecount="1"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="4" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="74" count="4" type="stmt"/>
        <line num="77" count="4" type="stmt"/>
        <line num="78" count="25" type="cond" truecount="2" falsecount="2"/>
        <line num="80" count="21" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="4" type="stmt"/>
        <line num="98" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="99" count="3" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="106" count="4" type="stmt"/>
        <line num="109" count="4" type="cond" truecount="4" falsecount="0"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="120" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="132" count="4" type="stmt"/>
        <line num="134" count="4" type="stmt"/>
        <line num="137" count="25" type="stmt"/>
        <line num="138" count="25" type="stmt"/>
        <line num="139" count="25" type="stmt"/>
        <line num="146" count="21" type="stmt"/>
        <line num="148" count="21" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="153" count="21" type="stmt"/>
        <line num="155" count="21" type="stmt"/>
        <line num="162" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="167" count="21" type="stmt"/>
        <line num="175" count="31" type="stmt"/>
        <line num="177" count="31" type="stmt"/>
        <line num="178" count="31" type="stmt"/>
        <line num="179" count="31" type="stmt"/>
        <line num="180" count="31" type="stmt"/>
        <line num="181" count="31" type="cond" truecount="1" falsecount="0"/>
        <line num="182" count="31" type="stmt"/>
        <line num="187" count="31" type="stmt"/>
        <line num="190" count="4" type="stmt"/>
        <line num="194" count="31" type="stmt"/>
      </file>
    </package>
    <package name="src.lib">
      <metrics statements="69" coveredstatements="68" conditionals="28" coveredconditionals="27" methods="21" coveredmethods="21"/>
      <file name="utils.ts" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/utils.ts">
        <metrics statements="69" coveredstatements="68" conditionals="28" coveredconditionals="27" methods="21" coveredmethods="21"/>
        <line num="1" count="7" type="stmt"/>
        <line num="2" count="7" type="stmt"/>
        <line num="3" count="7" type="stmt"/>
        <line num="5" count="80" type="stmt"/>
        <line num="6" count="80" type="stmt"/>
        <line num="10" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="11" count="3" type="stmt"/>
        <line num="14" count="2" type="stmt"/>
        <line num="15" count="2" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="22" count="2" type="stmt"/>
        <line num="23" count="2" type="stmt"/>
        <line num="26" count="2" type="stmt"/>
        <line num="27" count="2" type="stmt"/>
        <line num="31" count="3" type="stmt"/>
        <line num="32" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="33" count="1" type="stmt"/>
        <line num="36" count="4" type="stmt"/>
        <line num="37" count="4" type="stmt"/>
        <line num="41" count="2" type="stmt"/>
        <line num="42" count="2" type="stmt"/>
        <line num="44" count="4" type="stmt"/>
        <line num="45" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="46" count="4" type="stmt"/>
        <line num="47" count="4" type="stmt"/>
        <line num="53" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="54" count="3" type="stmt"/>
        <line num="55" count="18" type="stmt"/>
        <line num="56" count="18" type="stmt"/>
        <line num="58" count="18" type="cond" truecount="3" falsecount="0"/>
        <line num="59" count="6" type="cond" truecount="3" falsecount="0"/>
        <line num="60" count="0" type="stmt"/>
        <line num="65" count="6" type="stmt"/>
        <line num="66" count="6" type="stmt"/>
        <line num="67" count="6" type="stmt"/>
        <line num="70" count="4" type="stmt"/>
        <line num="71" count="4" type="stmt"/>
        <line num="75" count="4" type="stmt"/>
        <line num="76" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="78" count="3" type="stmt"/>
        <line num="79" count="3" type="stmt"/>
        <line num="80" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="87" count="3" type="stmt"/>
        <line num="88" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="90" count="2" type="stmt"/>
        <line num="91" count="2" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="97" count="2" type="stmt"/>
        <line num="98" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="100" count="2" type="stmt"/>
        <line num="101" count="2" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="108" count="6" type="stmt"/>
        <line num="109" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="110" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="111" count="3" type="stmt"/>
        <line num="115" count="6" type="stmt"/>
        <line num="116" count="6" type="stmt"/>
        <line num="134" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="137" count="8" type="stmt"/>
        <line num="138" count="8" type="cond" truecount="3" falsecount="0"/>
        <line num="139" count="6" type="cond" truecount="3" falsecount="0"/>
        <line num="140" count="4" type="stmt"/>
        <line num="143" count="9" type="stmt"/>
        <line num="144" count="9" type="stmt"/>
        <line num="145" count="9" type="cond" truecount="2" falsecount="0"/>
      </file>
    </package>
    <package name="src.lib.ts">
      <metrics statements="264" coveredstatements="67" conditionals="117" coveredconditionals="22" methods="40" coveredmethods="7"/>
      <file name="calendar.ts" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/calendar.ts">
        <metrics statements="204" coveredstatements="13" conditionals="94" coveredconditionals="0" methods="32" coveredmethods="0"/>
        <line num="1" count="3" type="stmt"/>
        <line num="2" count="3" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="118" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="132" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="151" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="152" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="176" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="186" count="1" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="200" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="202" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="219" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="256" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="257" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="266" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="280" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="301" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="302" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="303" count="0" type="stmt"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="305" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="306" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="331" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="332" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="342" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="343" count="0" type="stmt"/>
        <line num="344" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="345" count="0" type="stmt"/>
        <line num="346" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="350" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="354" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="355" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="385" count="1" type="stmt"/>
        <line num="386" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="387" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="410" count="0" type="stmt"/>
        <line num="411" count="0" type="stmt"/>
        <line num="412" count="0" type="stmt"/>
        <line num="413" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="414" count="0" type="stmt"/>
        <line num="415" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="416" count="0" type="stmt"/>
        <line num="417" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="420" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="421" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="424" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="425" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="427" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="428" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="437" count="0" type="stmt"/>
        <line num="438" count="0" type="stmt"/>
        <line num="439" count="0" type="stmt"/>
        <line num="440" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="441" count="0" type="stmt"/>
        <line num="442" count="0" type="stmt"/>
        <line num="443" count="0" type="stmt"/>
        <line num="444" count="0" type="stmt"/>
      </file>
      <file name="storage.ts" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/storage.ts">
        <metrics statements="34" coveredstatements="34" conditionals="15" coveredconditionals="15" methods="4" coveredmethods="4"/>
        <line num="1" count="10" type="stmt"/>
        <line num="11" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="13" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="14" count="1" type="stmt"/>
        <line num="17" count="8" type="cond" truecount="3" falsecount="0"/>
        <line num="18" count="3" type="stmt"/>
        <line num="21" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="22" count="3" type="stmt"/>
        <line num="25" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="26" count="1" type="stmt"/>
        <line num="29" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="30" count="3" type="stmt"/>
        <line num="34" count="15" type="stmt"/>
        <line num="35" count="14" type="cond" truecount="1" falsecount="0"/>
        <line num="36" count="1" type="stmt"/>
        <line num="45" count="13" type="stmt"/>
        <line num="46" count="13" type="stmt"/>
        <line num="47" count="13" type="stmt"/>
        <line num="48" count="13" type="stmt"/>
        <line num="49" count="13" type="stmt"/>
        <line num="52" count="13" type="stmt"/>
        <line num="53" count="39" type="cond" truecount="1" falsecount="0"/>
        <line num="54" count="8" type="stmt"/>
        <line num="55" count="8" type="stmt"/>
        <line num="57" count="3" type="stmt"/>
        <line num="58" count="3" type="stmt"/>
        <line num="62" count="13" type="stmt"/>
        <line num="71" count="4" type="stmt"/>
        <line num="72" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="74" count="2" type="stmt"/>
        <line num="75" count="2" type="stmt"/>
        <line num="76" count="2" type="stmt"/>
        <line num="77" count="2" type="stmt"/>
        <line num="78" count="2" type="stmt"/>
      </file>
      <file name="user.ts" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/user.ts">
        <metrics statements="20" coveredstatements="20" conditionals="8" coveredconditionals="7" methods="3" coveredmethods="3"/>
        <line num="1" count="4" type="stmt"/>
        <line num="2" count="4" type="stmt"/>
        <line num="3" count="4" type="stmt"/>
        <line num="5" count="10" type="stmt"/>
        <line num="6" count="9" type="stmt"/>
        <line num="10" count="8" type="stmt"/>
        <line num="12" count="8" type="stmt"/>
        <line num="13" count="8" type="stmt"/>
        <line num="15" count="8" type="stmt"/>
        <line num="23" count="8" type="stmt"/>
        <line num="28" count="40" type="cond" truecount="1" falsecount="1"/>
        <line num="37" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="39" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="40" count="6" type="stmt"/>
        <line num="44" count="2" type="stmt"/>
        <line num="48" count="2" type="cond" truecount="3" falsecount="0"/>
        <line num="49" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="55" count="3" type="stmt"/>
        <line num="56" count="2" type="stmt"/>
      </file>
      <file name="worker.ts" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/worker.ts">
        <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.pages">
      <metrics statements="59" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="8" coveredmethods="0"/>
      <file name="AboutPage.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/pages/AboutPage.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
      </file>
      <file name="ChangelogsPage.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/pages/ChangelogsPage.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
      </file>
      <file name="LoginPage.tsx" path="/Users/<USER>/Github/kma-schedule-ngosangns/src/pages/LoginPage.tsx">
        <metrics statements="56" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>


> kma-schedule-ngosangns@0.0.1 test
> jest --coverage --ci --watchAll=false

FAIL src/__tests__/lib/ts/storage.test.ts
  storage utilities
    saveData
      ✓ should save signInToken to localStorage (2 ms)
      ✓ should save mainForm to localStorage (1 ms)
      ✓ should save semesters to localStorage
      ✓ should save calendar to localStorage
      ✓ should save student to localStorage
      ✓ should save multiple data types at once (1 ms)
      ✓ should not save empty or falsy values
      ✓ should handle SSR environment gracefully
    loadData
      ✓ should load all data from localStorage (1 ms)
      ✓ should return null values when data not found
      ✕ should handle invalid JSON gracefully
      ✓ should handle SSR environment
      ✓ should parse JSON data correctly
    clearData
      ✓ should clear all stored data
      ✓ should handle SSR environment gracefully
    integration tests
      ✓ should save and load data correctly
      ✓ should clear data correctly

  ● storage utilities › loadData › should handle invalid JSON gracefully

    SyntaxError: Unexpected token 'i', "invalid-json" is not valid JSON
        at JSON.parse (<anonymous>)

      50 |
      51 | 	return {
    > 52 | 		calendar: calendar ? JSON.parse(calendar) : null,
         | 		                          ^
      53 | 		student: student ? student : null,
      54 | 		semesters: semesters ? JSON.parse(semesters) : null,
      55 | 		mainForm: mainForm ? JSON.parse(mainForm) : null,

      at parse (src/lib/ts/storage.ts:52:29)
      at Object.<anonymous> (src/__tests__/lib/ts/storage.test.ts:166:30)

FAIL src/__tests__/hooks/use-toast.test.ts
  useToast
    ✓ should initialize with empty toasts (6 ms)
    ✓ should add a toast (5 ms)
    ✕ should add multiple toasts (2 ms)
    ✓ should respect toast limit (1 ms)
    ✕ should dismiss a toast (1 ms)
    ✕ should dismiss all toasts when no id provided (1 ms)
    ✕ should update an existing toast (2 ms)
    ✓ should handle toast with action
    ✓ should handle different toast variants (2 ms)
    ✕ should generate unique ids for toasts (1 ms)
    ✕ should handle toast removal after timeout
    ✓ should handle empty toast props (1 ms)
    ✓ should not dismiss non-existent toast

  ● useToast › should add multiple toasts

    expect(received).toHaveLength(expected)

    Expected length: 2
    Received length: 1
    Received array:  [{"id": "3", "onOpenChange": [Function onOpenChange], "open": true, "title": "Toast 2"}]

      39 |     })
      40 |
    > 41 |     expect(result.current.toasts).toHaveLength(2)
         |                                   ^
      42 |     expect(result.current.toasts[0].title).toBe('Toast 1')
      43 |     expect(result.current.toasts[1].title).toBe('Toast 2')
      44 |   })

      at Object.toHaveLength (src/__tests__/hooks/use-toast.test.ts:41:35)

  ● useToast › should dismiss a toast

    expect(received).toHaveLength(expected)

    Expected length: 0
    Received length: 1
    Received array:  [{"id": "6", "onOpenChange": [Function onOpenChange], "open": true, "title": "Test Toast"}]

      74 |     })
      75 |
    > 76 |     expect(result.current.toasts).toHaveLength(0)
         |                                   ^
      77 |   })
      78 |
      79 |   it('should dismiss all toasts when no id provided', () => {

      at Object.toHaveLength (src/__tests__/hooks/use-toast.test.ts:76:35)

  ● useToast › should dismiss all toasts when no id provided

    expect(received).toHaveLength(expected)

    Expected length: 0
    Received length: 1
    Received array:  [{"id": "8", "onOpenChange": [Function onOpenChange], "open": false, "title": "Toast 2"}]

      91 |     })
      92 |
    > 93 |     expect(result.current.toasts).toHaveLength(0)
         |                                   ^
      94 |   })
      95 |
      96 |   it('should update an existing toast', () => {

      at Object.toHaveLength (src/__tests__/hooks/use-toast.test.ts:93:35)

  ● useToast › should update an existing toast

    expect(received).toMatchObject(expected)

    - Expected  - 1
    + Received  + 1

      Object {
        "description": "Updated Description",
    -   "id": "8",
    +   "id": "10",
        "title": "Updated Title",
      }

      113 |
      114 |     expect(result.current.toasts).toHaveLength(1)
    > 115 |     expect(result.current.toasts[0]).toMatchObject({
          |                                      ^
      116 |       id: toastId,
      117 |       title: 'Updated Title',
      118 |       description: 'Updated Description',

      at Object.toMatchObject (src/__tests__/hooks/use-toast.test.ts:115:38)

  ● useToast › should generate unique ids for toasts

    expect(received).toBe(expected) // Object.is equality

    Expected: 5
    Received: 1

      176 |     // All ids should be unique (though only last one remains due to limit)
      177 |     const uniqueIds = new Set(ids)
    > 178 |     expect(uniqueIds.size).toBe(ids.length)
          |                            ^
      179 |   })
      180 |
      181 |   it('should handle toast removal after timeout', (done) => {

      at Object.toBe (src/__tests__/hooks/use-toast.test.ts:178:28)

  ● useToast › should handle toast removal after timeout

    expect(received).toHaveLength(expected)

    Expected length: 0
    Received length: 1
    Received array:  [{"id": "19", "onOpenChange": [Function onOpenChange], "open": false, "title": "Test Toast"}]

      195 |     })
      196 |
    > 197 |     expect(result.current.toasts).toHaveLength(0)
          |                                   ^
      198 |     done()
      199 |   })
      200 |

      at Object.toHaveLength (src/__tests__/hooks/use-toast.test.ts:197:35)

PASS src/__tests__/lib/ts/user.test.ts
  user utilities
    login
      ✓ should login successfully with valid credentials (1 ms)
      ✓ should handle login with cookie in Set-Cookie header
      ✓ should handle login with response text as cookie
      ✓ should return response text when no cookie found
      ✓ should convert username to uppercase (1 ms)
      ✓ should include form fields in POST request
      ✓ should handle network errors (2 ms)
      ✓ should handle invalid HTML response
    logout
      ✓ should call clearData (2 ms)
      ✓ should not throw errors
    integration tests
      ✓ should handle complete login flow (1 ms)

PASS src/__tests__/integration/login-flow.test.tsx
  Login Flow Integration
    ✓ should handle successful login flow (89 ms)
    ✓ should handle failed login flow (59 ms)
    ✓ should disable login button during processing (1 ms)
    ✓ should handle empty credentials (11 ms)
    ✓ should handle network errors during login (57 ms)
    ✓ should clear form after successful login (64 ms)
    ✓ should maintain form state during failed login (63 ms)

  console.error
    Error: Uncaught [Error: useApp must be used within an AppProvider]
        at reportException (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/helpers/runtime-script-errors.js:66:24)
        at innerInvokeEventListeners (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:353:9)
        at invokeEventListeners (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:286:3)
        at HTMLUnknownElementImpl._dispatch (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:233:9)
        at HTMLUnknownElementImpl.dispatchEvent (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:104:17)
        at HTMLUnknownElement.dispatchEvent (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/generated/EventTarget.js:241:34)
        at Object.invokeGuardedCallbackDev (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:4213:16)
        at invokeGuardedCallback (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:4277:31)
        at beginWork$1 (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:27490:7)
        at performUnitOfWork (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
        at workLoopSync (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
        at renderRootSync (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
        at performConcurrentWorkOnRoot (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:25777:74)
        at flushActQueue (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react/cjs/react.development.js:2667:24)
        at act (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react/cjs/react.development.js:2582:11)
        at /Users/<USER>/Github/kma-schedule-ngosangns/node_modules/@testing-library/react/dist/act-compat.js:47:25
        at renderRoot (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/@testing-library/react/dist/pure.js:190:26)
        at render (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/@testing-library/react/dist/pure.js:292:10)
        at renderHook (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/@testing-library/react/dist/pure.js:340:7)
        at /Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/contexts/AppContext.test.tsx:86:15
        at Object.<anonymous> (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/expect/build/toThrowMatchers.js:74:11)
        at Object.throwingMatcher [as toThrow] (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/expect/build/index.js:320:21)
        at Object.toThrow (/Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/contexts/AppContext.test.tsx:87:7)
        at Promise.then.completed (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/utils.js:298:28)
        at new Promise (<anonymous>)
        at callAsyncCircusFn (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/utils.js:231:10)
        at _callCircusTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:316:40)
        at _runTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:252:3)
        at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:126:9)
        at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:121:9)
        at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:121:9)
        at run (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:71:3)
        at runAndTransformResultsToJestFormat (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
        at jestAdapter (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
        at runTestInternal (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-runner/build/runTest.js:367:16)
        at runTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-runner/build/runTest.js:444:34) {
      detail: Error: useApp must be used within an AppProvider
          at useApp (/Users/<USER>/Github/kma-schedule-ngosangns/src/contexts/AppContext.tsx:188:9)
          at /Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/contexts/AppContext.test.tsx:86:28
          at TestComponent (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/@testing-library/react/dist/pure.js:331:27)
          at renderWithHooks (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
          at mountIndeterminateComponent (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
          at beginWork (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
          at HTMLUnknownElement.callCallback (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:4164:14)
          at HTMLUnknownElement.callTheUserObjectsOperation (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/generated/EventListener.js:26:30)
          at innerInvokeEventListeners (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:350:25)
          at invokeEventListeners (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:286:3)
          at HTMLUnknownElementImpl._dispatch (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:233:9)
          at HTMLUnknownElementImpl.dispatchEvent (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:104:17)
          at HTMLUnknownElement.dispatchEvent (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/generated/EventTarget.js:241:34)
          at Object.invokeGuardedCallbackDev (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:4213:16)
          at invokeGuardedCallback (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:4277:31)
          at beginWork$1 (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:27490:7)
          at performUnitOfWork (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
          at workLoopSync (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
          at renderRootSync (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
          at performConcurrentWorkOnRoot (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:25777:74)
          at flushActQueue (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react/cjs/react.development.js:2667:24)
          at act (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react/cjs/react.development.js:2582:11)
          at /Users/<USER>/Github/kma-schedule-ngosangns/node_modules/@testing-library/react/dist/act-compat.js:47:25
          at renderRoot (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/@testing-library/react/dist/pure.js:190:26)
          at render (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/@testing-library/react/dist/pure.js:292:10)
          at renderHook (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/@testing-library/react/dist/pure.js:340:7)
          at /Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/contexts/AppContext.test.tsx:86:15
          at Object.<anonymous> (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/expect/build/toThrowMatchers.js:74:11)
          at Object.throwingMatcher [as toThrow] (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/expect/build/index.js:320:21)
          at Object.toThrow (/Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/contexts/AppContext.test.tsx:87:7)
          at Promise.then.completed (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/utils.js:298:28)
          at new Promise (<anonymous>)
          at callAsyncCircusFn (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/utils.js:231:10)
          at _callCircusTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:316:40)
          at _runTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:252:3)
          at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:126:9)
          at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:121:9)
          at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:121:9)
          at run (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:71:3)
          at runAndTransformResultsToJestFormat (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
          at jestAdapter (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
          at runTestInternal (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-runner/build/runTest.js:367:16)
          at runTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-runner/build/runTest.js:444:34),
      type: 'unhandled exception'
    }

      132 | 			return;
      133 | 		}
    > 134 | 		originalError.call(console, ...args);
          | 		              ^
      135 | 	};
      136 | });
      137 |

      at console.call [as error] (jest.setup.js:134:17)
      at reportException (node_modules/jsdom/lib/jsdom/living/helpers/runtime-script-errors.js:70:28)
      at innerInvokeEventListeners (node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:353:9)
      at invokeEventListeners (node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:286:3)
      at HTMLUnknownElementImpl._dispatch (node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:233:9)
      at HTMLUnknownElementImpl.dispatchEvent (node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:104:17)
      at HTMLUnknownElement.dispatchEvent (node_modules/jsdom/lib/jsdom/living/generated/EventTarget.js:241:34)
      at Object.invokeGuardedCallbackDev (node_modules/react-dom/cjs/react-dom.development.js:4213:16)
      at invokeGuardedCallback (node_modules/react-dom/cjs/react-dom.development.js:4277:31)
      at beginWork$1 (node_modules/react-dom/cjs/react-dom.development.js:27490:7)
      at performUnitOfWork (node_modules/react-dom/cjs/react-dom.development.js:26599:12)
      at workLoopSync (node_modules/react-dom/cjs/react-dom.development.js:26505:5)
      at renderRootSync (node_modules/react-dom/cjs/react-dom.development.js:26473:7)
      at performConcurrentWorkOnRoot (node_modules/react-dom/cjs/react-dom.development.js:25777:74)
      at flushActQueue (node_modules/react/cjs/react.development.js:2667:24)
      at act (node_modules/react/cjs/react.development.js:2582:11)
      at node_modules/@testing-library/react/dist/act-compat.js:47:25
      at renderRoot (node_modules/@testing-library/react/dist/pure.js:190:26)
      at render (node_modules/@testing-library/react/dist/pure.js:292:10)
      at renderHook (node_modules/@testing-library/react/dist/pure.js:340:7)
      at src/__tests__/contexts/AppContext.test.tsx:86:15
      at Object.<anonymous> (node_modules/expect/build/toThrowMatchers.js:74:11)
      at Object.throwingMatcher [as toThrow] (node_modules/expect/build/index.js:320:21)
      at Object.toThrow (src/__tests__/contexts/AppContext.test.tsx:87:7)

  console.error
    Error: Uncaught [Error: useApp must be used within an AppProvider]
        at reportException (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/helpers/runtime-script-errors.js:66:24)
        at innerInvokeEventListeners (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:353:9)
        at invokeEventListeners (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:286:3)
        at HTMLUnknownElementImpl._dispatch (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:233:9)
        at HTMLUnknownElementImpl.dispatchEvent (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:104:17)
        at HTMLUnknownElement.dispatchEvent (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/generated/EventTarget.js:241:34)
        at Object.invokeGuardedCallbackDev (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:4213:16)
        at invokeGuardedCallback (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:4277:31)
        at beginWork$1 (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:27490:7)
        at performUnitOfWork (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
        at workLoopSync (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
        at renderRootSync (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
        at recoverFromConcurrentError (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
        at performConcurrentWorkOnRoot (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
        at flushActQueue (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react/cjs/react.development.js:2667:24)
        at act (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react/cjs/react.development.js:2582:11)
        at /Users/<USER>/Github/kma-schedule-ngosangns/node_modules/@testing-library/react/dist/act-compat.js:47:25
        at renderRoot (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/@testing-library/react/dist/pure.js:190:26)
        at render (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/@testing-library/react/dist/pure.js:292:10)
        at renderHook (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/@testing-library/react/dist/pure.js:340:7)
        at /Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/contexts/AppContext.test.tsx:86:15
        at Object.<anonymous> (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/expect/build/toThrowMatchers.js:74:11)
        at Object.throwingMatcher [as toThrow] (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/expect/build/index.js:320:21)
        at Object.toThrow (/Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/contexts/AppContext.test.tsx:87:7)
        at Promise.then.completed (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/utils.js:298:28)
        at new Promise (<anonymous>)
        at callAsyncCircusFn (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/utils.js:231:10)
        at _callCircusTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:316:40)
        at _runTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:252:3)
        at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:126:9)
        at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:121:9)
        at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:121:9)
        at run (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:71:3)
        at runAndTransformResultsToJestFormat (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
        at jestAdapter (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
        at runTestInternal (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-runner/build/runTest.js:367:16)
        at runTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-runner/build/runTest.js:444:34) {
      detail: Error: useApp must be used within an AppProvider
          at useApp (/Users/<USER>/Github/kma-schedule-ngosangns/src/contexts/AppContext.tsx:188:9)
          at /Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/contexts/AppContext.test.tsx:86:28
          at TestComponent (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/@testing-library/react/dist/pure.js:331:27)
          at renderWithHooks (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
          at mountIndeterminateComponent (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
          at beginWork (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
          at HTMLUnknownElement.callCallback (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:4164:14)
          at HTMLUnknownElement.callTheUserObjectsOperation (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/generated/EventListener.js:26:30)
          at innerInvokeEventListeners (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:350:25)
          at invokeEventListeners (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:286:3)
          at HTMLUnknownElementImpl._dispatch (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:233:9)
          at HTMLUnknownElementImpl.dispatchEvent (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:104:17)
          at HTMLUnknownElement.dispatchEvent (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jsdom/lib/jsdom/living/generated/EventTarget.js:241:34)
          at Object.invokeGuardedCallbackDev (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:4213:16)
          at invokeGuardedCallback (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:4277:31)
          at beginWork$1 (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:27490:7)
          at performUnitOfWork (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
          at workLoopSync (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
          at renderRootSync (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
          at recoverFromConcurrentError (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
          at performConcurrentWorkOnRoot (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
          at flushActQueue (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react/cjs/react.development.js:2667:24)
          at act (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/react/cjs/react.development.js:2582:11)
          at /Users/<USER>/Github/kma-schedule-ngosangns/node_modules/@testing-library/react/dist/act-compat.js:47:25
          at renderRoot (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/@testing-library/react/dist/pure.js:190:26)
          at render (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/@testing-library/react/dist/pure.js:292:10)
          at renderHook (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/@testing-library/react/dist/pure.js:340:7)
          at /Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/contexts/AppContext.test.tsx:86:15
          at Object.<anonymous> (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/expect/build/toThrowMatchers.js:74:11)
          at Object.throwingMatcher [as toThrow] (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/expect/build/index.js:320:21)
          at Object.toThrow (/Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/contexts/AppContext.test.tsx:87:7)
          at Promise.then.completed (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/utils.js:298:28)
          at new Promise (<anonymous>)
          at callAsyncCircusFn (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/utils.js:231:10)
          at _callCircusTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:316:40)
          at _runTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:252:3)
          at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:126:9)
          at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:121:9)
          at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:121:9)
          at run (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:71:3)
          at runAndTransformResultsToJestFormat (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
          at jestAdapter (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
          at runTestInternal (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-runner/build/runTest.js:367:16)
          at runTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-runner/build/runTest.js:444:34),
      type: 'unhandled exception'
    }

      132 | 			return;
      133 | 		}
    > 134 | 		originalError.call(console, ...args);
          | 		              ^
      135 | 	};
      136 | });
      137 |

      at console.call [as error] (jest.setup.js:134:17)
      at reportException (node_modules/jsdom/lib/jsdom/living/helpers/runtime-script-errors.js:70:28)
      at innerInvokeEventListeners (node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:353:9)
      at invokeEventListeners (node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:286:3)
      at HTMLUnknownElementImpl._dispatch (node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:233:9)
      at HTMLUnknownElementImpl.dispatchEvent (node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:104:17)
      at HTMLUnknownElement.dispatchEvent (node_modules/jsdom/lib/jsdom/living/generated/EventTarget.js:241:34)
      at Object.invokeGuardedCallbackDev (node_modules/react-dom/cjs/react-dom.development.js:4213:16)
      at invokeGuardedCallback (node_modules/react-dom/cjs/react-dom.development.js:4277:31)
      at beginWork$1 (node_modules/react-dom/cjs/react-dom.development.js:27490:7)
      at performUnitOfWork (node_modules/react-dom/cjs/react-dom.development.js:26599:12)
      at workLoopSync (node_modules/react-dom/cjs/react-dom.development.js:26505:5)
      at renderRootSync (node_modules/react-dom/cjs/react-dom.development.js:26473:7)
      at recoverFromConcurrentError (node_modules/react-dom/cjs/react-dom.development.js:25889:20)
      at performConcurrentWorkOnRoot (node_modules/react-dom/cjs/react-dom.development.js:25789:22)
      at flushActQueue (node_modules/react/cjs/react.development.js:2667:24)
      at act (node_modules/react/cjs/react.development.js:2582:11)
      at node_modules/@testing-library/react/dist/act-compat.js:47:25
      at renderRoot (node_modules/@testing-library/react/dist/pure.js:190:26)
      at render (node_modules/@testing-library/react/dist/pure.js:292:10)
      at renderHook (node_modules/@testing-library/react/dist/pure.js:340:7)
      at src/__tests__/contexts/AppContext.test.tsx:86:15
      at Object.<anonymous> (node_modules/expect/build/toThrowMatchers.js:74:11)
      at Object.throwingMatcher [as toThrow] (node_modules/expect/build/index.js:320:21)
      at Object.toThrow (src/__tests__/contexts/AppContext.test.tsx:87:7)

  console.error
    The above error occurred in the <TestComponent> component:
    
        at TestComponent (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/@testing-library/react/dist/pure.js:329:5)
    
    Consider adding an error boundary to your tree to customize error handling behavior.
    Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.

      132 | 			return;
      133 | 		}
    > 134 | 		originalError.call(console, ...args);
          | 		              ^
      135 | 	};
      136 | });
      137 |

      at console.call [as error] (jest.setup.js:134:17)
      at logCapturedError (node_modules/react-dom/cjs/react-dom.development.js:18704:23)
      at update.callback (node_modules/react-dom/cjs/react-dom.development.js:18737:5)
      at callCallback (node_modules/react-dom/cjs/react-dom.development.js:15036:12)
      at commitUpdateQueue (node_modules/react-dom/cjs/react-dom.development.js:15057:9)
      at commitLayoutEffectOnFiber (node_modules/react-dom/cjs/react-dom.development.js:23430:13)
      at commitLayoutMountEffects_complete (node_modules/react-dom/cjs/react-dom.development.js:24727:9)
      at commitLayoutEffects_begin (node_modules/react-dom/cjs/react-dom.development.js:24713:7)
      at commitLayoutEffects (node_modules/react-dom/cjs/react-dom.development.js:24651:3)
      at commitRootImpl (node_modules/react-dom/cjs/react-dom.development.js:26862:5)
      at commitRoot (node_modules/react-dom/cjs/react-dom.development.js:26721:5)
      at finishConcurrentRender (node_modules/react-dom/cjs/react-dom.development.js:25931:9)
      at performConcurrentWorkOnRoot (node_modules/react-dom/cjs/react-dom.development.js:25848:7)
      at flushActQueue (node_modules/react/cjs/react.development.js:2667:24)
      at act (node_modules/react/cjs/react.development.js:2582:11)
      at node_modules/@testing-library/react/dist/act-compat.js:47:25
      at renderRoot (node_modules/@testing-library/react/dist/pure.js:190:26)
      at render (node_modules/@testing-library/react/dist/pure.js:292:10)
      at renderHook (node_modules/@testing-library/react/dist/pure.js:340:7)
      at src/__tests__/contexts/AppContext.test.tsx:86:15
      at Object.<anonymous> (node_modules/expect/build/toThrowMatchers.js:74:11)
      at Object.throwingMatcher [as toThrow] (node_modules/expect/build/index.js:320:21)
      at Object.toThrow (src/__tests__/contexts/AppContext.test.tsx:87:7)

PASS src/__tests__/contexts/AppContext.test.tsx
  AppContext
    AppProvider
      ✓ should provide initial state (2 ms)
      ✓ should load data from storage on mount (1 ms)
      ✓ should save data when authenticated with calendar
    useApp hook
      ✓ should throw error when used outside provider (32 ms)
      ✓ should provide state and dispatch (1 ms)
    useAuth hook
      ✓ should provide auth state and methods (1 ms)
      ✓ should handle login (1 ms)
      ✓ should handle logout (1 ms)
      ✓ should handle loading state
      ✓ should handle error state (1 ms)
    useCalendar hook
      ✓ should provide calendar state and methods
      ✓ should handle setting calendar (1 ms)
      ✓ should handle setting student
    useUI hook
      ✓ should provide UI state and methods (3 ms)
      ✓ should handle theme change
      ✓ should handle sidebar toggle (1 ms)
      ✓ should handle view change
    Reducer actions
      ✓ should handle AUTH_START action (1 ms)
      ✓ should handle AUTH_SUCCESS action
      ✓ should handle AUTH_ERROR action (1 ms)
      ✓ should handle AUTH_LOGOUT action
      ✓ should handle LOAD_FROM_STORAGE action (1 ms)

PASS src/__tests__/integration/calendar-flow.test.tsx
  Calendar Flow Integration
    ✓ should display login prompt when not authenticated (2 ms)
    ✓ should display calendar data when authenticated (5 ms)
    ✓ should handle semester change (33 ms)
    ✓ should handle calendar export (7 ms)
    ✓ should handle logout (9 ms)
    ✓ should display no calendar message when no data available (1 ms)
    ✓ should disable export when no calendar or student data (9 ms)
    ✓ should maintain UI state during semester change (23 ms)

PASS src/__tests__/components/ui/input.test.tsx
  Input
    ✓ should render with default props (10 ms)
    ✓ should render with placeholder (1 ms)
    ✓ should handle text input (28 ms)
    ✓ should handle onChange events (16 ms)
    ✓ should be disabled when disabled prop is true (2 ms)
    ✓ should not accept input when disabled (4 ms)
    ✓ should handle different input types (5 ms)
    ✓ should merge custom className with default classes (2 ms)
    ✓ should forward ref correctly (1 ms)
    ✓ should pass through HTML input attributes (1 ms)
    ✓ should handle focus and blur events (17 ms)
    ✓ should handle keyboard events (3 ms)
    ✓ should support controlled input (25 ms)
    ✓ should support uncontrolled input with defaultValue (2 ms)
    ✓ should have correct accessibility attributes (3 ms)
    ✓ should support form validation attributes (2 ms)
    ✓ should handle file input type (1 ms)
    ✓ should support autoComplete (1 ms)
    ✓ should have correct display name
    focus management
      ✓ should be focusable (8 ms)
      ✓ should show focus styles (8 ms)
    placeholder styling
      ✓ should apply placeholder styles

PASS src/__tests__/hooks/use-notifications.test.ts
  useNotifications
    ✓ should provide showSuccess function (2 ms)
    ✓ should provide showError function
    ✓ should provide showWarning function
    ✓ should provide showInfo function (1 ms)
    ✓ should handle empty messages (1 ms)
    ✓ should handle undefined descriptions
    ✓ should create new function instances on re-renders
    ✓ should work with different toast implementations
    showSuccess
      ✓ should call toast with success variant
      ✓ should call toast with success message and description (1 ms)
    showError
      ✓ should call toast with destructive variant
      ✓ should call toast with error message and description
    showWarning
      ✓ should call toast with warning variant
      ✓ should call toast with warning message and description
    showInfo
      ✓ should call toast with info variant (1 ms)
      ✓ should call toast with info message and description (3 ms)

PASS src/__tests__/components/ui/button.test.tsx
  Button
    ✓ should render with default props (8 ms)
    ✓ should render children correctly (1 ms)
    ✓ should handle click events (10 ms)
    ✓ should be disabled when disabled prop is true (4 ms)
    ✓ should not call onClick when disabled (11 ms)
    ✓ should merge custom className with default classes (2 ms)
    ✓ should forward ref correctly (1 ms)
    ✓ should pass through HTML button attributes (1 ms)
    ✓ should handle keyboard events (2 ms)
    ✓ should have correct accessibility attributes (2 ms)
    ✓ should support focus management (8 ms)
    ✓ should have correct display name
    variants
      ✓ should apply default variant classes (3 ms)
      ✓ should apply destructive variant classes (2 ms)
      ✓ should apply outline variant classes (3 ms)
      ✓ should apply secondary variant classes (2 ms)
      ✓ should apply ghost variant classes (3 ms)
      ✓ should apply link variant classes (2 ms)
    sizes
      ✓ should apply default size classes (2 ms)
      ✓ should apply small size classes (2 ms)
      ✓ should apply large size classes (2 ms)
      ✓ should apply icon size classes (2 ms)
    asChild prop
      ✓ should render as child component when asChild is true (3 ms)
      ✓ should render as button when asChild is false (2 ms)
    combinations
      ✓ should apply multiple props correctly (2 ms)

  console.error
    Login error: Error: Login failed
        at Object.<anonymous> (/Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/hooks/use-calendar-data.test.ts:116:51)
        at Promise.then.completed (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/utils.js:298:28)
        at new Promise (<anonymous>)
        at callAsyncCircusFn (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/utils.js:231:10)
        at _callCircusTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:316:40)
        at _runTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:252:3)
        at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:126:9)
        at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:121:9)
        at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:121:9)
        at run (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:71:3)
        at runAndTransformResultsToJestFormat (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
        at jestAdapter (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
        at runTestInternal (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-runner/build/runTest.js:367:16)
        at runTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-runner/build/runTest.js:444:34)

      132 | 			return;
      133 | 		}
    > 134 | 		originalError.call(console, ...args);
          | 		              ^
      135 | 	};
      136 | });
      137 |

      at console.call [as error] (jest.setup.js:134:17)
      at Object.error [as loginWithCredentials] (src/hooks/use-calendar-data.ts:56:13)
      at src/__tests__/hooks/use-calendar-data.test.ts:123:23

  console.error
    Manual response processing error: Error: Processing failed
        at Object.<anonymous> (/Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/hooks/use-calendar-data.test.ts:175:57)
        at Promise.then.completed (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/utils.js:298:28)
        at new Promise (<anonymous>)
        at callAsyncCircusFn (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/utils.js:231:10)
        at _callCircusTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:316:40)
        at _runTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:252:3)
        at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:126:9)
        at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:121:9)
        at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:121:9)
        at run (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:71:3)
        at runAndTransformResultsToJestFormat (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
        at jestAdapter (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
        at runTestInternal (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-runner/build/runTest.js:367:16)
        at runTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-runner/build/runTest.js:444:34)

      132 | 			return;
      133 | 		}
    > 134 | 		originalError.call(console, ...args);
          | 		              ^
      135 | 	};
      136 | });
      137 |

      at console.call [as error] (jest.setup.js:134:17)
      at Object.error [as processManualData] (src/hooks/use-calendar-data.ts:97:13)
      at src/__tests__/hooks/use-calendar-data.test.ts:182:25

  console.error
    Semester change error: Error: Fetch failed
        at Object.<anonymous> (/Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/hooks/use-calendar-data.test.ts:234:63)
        at Promise.then.completed (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/utils.js:298:28)
        at new Promise (<anonymous>)
        at callAsyncCircusFn (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/utils.js:231:10)
        at _callCircusTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:316:40)
        at _runTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:252:3)
        at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:126:9)
        at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:121:9)
        at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:121:9)
        at run (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:71:3)
        at runAndTransformResultsToJestFormat (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
        at jestAdapter (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
        at runTestInternal (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-runner/build/runTest.js:367:16)
        at runTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-runner/build/runTest.js:444:34)

      132 | 			return;
      133 | 		}
    > 134 | 		originalError.call(console, ...args);
          | 		              ^
      135 | 	};
      136 | });
      137 |

      at console.call [as error] (jest.setup.js:134:17)
      at Object.error [as changeSemester] (src/hooks/use-calendar-data.ts:146:13)
      at src/__tests__/hooks/use-calendar-data.test.ts:241:24

  console.error
    Export calendar error: Error: Export failed
        at /Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/hooks/use-calendar-data.test.ts:263:15
        at /Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-mock/build/index.js:397:39
        at /Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-mock/build/index.js:404:13
        at mockConstructor (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-mock/build/index.js:148:19)
        at exportToGoogleCalendar (eval at _createMockFunction (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-mock/build/index.js:566:31), <anonymous>:3:68)
        at Object.exportCalendar (/Users/<USER>/Github/kma-schedule-ngosangns/src/hooks/use-calendar-data.ts:160:27)
        at Object.exportCalendar (/Users/<USER>/Github/kma-schedule-ngosangns/src/__tests__/hooks/use-calendar-data.test.ts:268:43)
        at Promise.then.completed (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/utils.js:298:28)
        at new Promise (<anonymous>)
        at callAsyncCircusFn (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/utils.js:231:10)
        at _callCircusTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:316:40)
        at _runTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:252:3)
        at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:126:9)
        at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:121:9)
        at _runTestsForDescribeBlock (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:121:9)
        at run (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/run.js:71:3)
        at runAndTransformResultsToJestFormat (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
        at jestAdapter (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
        at runTestInternal (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-runner/build/runTest.js:367:16)
        at runTest (/Users/<USER>/Github/kma-schedule-ngosangns/node_modules/jest-runner/build/runTest.js:444:34)

      132 | 			return;
      133 | 		}
    > 134 | 		originalError.call(console, ...args);
          | 		              ^
      135 | 	};
      136 | });
      137 |

      at console.call [as error] (jest.setup.js:134:17)
      at Object.error [as exportCalendar] (src/hooks/use-calendar-data.ts:164:13)
      at Object.exportCalendar (src/__tests__/hooks/use-calendar-data.test.ts:268:43)

PASS src/__tests__/hooks/use-calendar-data.test.ts
  useCalendarData
    ✓ should initialize with isProcessing false (5 ms)
    ✓ should provide all expected functions (1 ms)
    ✓ should maintain stable function references
    loginWithCredentials
      ✓ should login successfully with valid credentials (1 ms)
      ✓ should handle login failure (8 ms)
      ✓ should save data after successful login (1 ms)
    processManualData
      ✓ should process manual data successfully (1 ms)
      ✓ should handle manual data processing failure (1 ms)
    changeSemester
      ✓ should return early if semester is the same (1 ms)
      ✓ should change semester successfully (1 ms)
      ✓ should handle semester change failure (1 ms)
    exportCalendar
      ✓ should export calendar successfully (1 ms)
      ✓ should handle export failure (3 ms)
    logout
      ✓ should logout successfully (1 ms)

PASS src/__tests__/components/ui/loading-spinner.test.tsx
  LoadingSpinner
    ✓ should render with default props (6 ms)
    ✓ should render with text (1 ms)
    ✓ should not render text when not provided
    ✓ should merge custom className with default classes
    ✓ should render with both size and text (1 ms)
    ✓ should have correct structure (1 ms)
    ✓ should apply animation class
    sizes
      ✓ should apply small size classes (1 ms)
      ✓ should apply medium size classes (default)
      ✓ should apply large size classes (1 ms)
      ✓ should default to medium size when size not specified
  PageLoader
    ✓ should render with default text (1 ms)
    ✓ should render with custom text (1 ms)
    ✓ should have correct container styling (1 ms)
    ✓ should use large size spinner
    ✓ should contain LoadingSpinner component (1 ms)
    ✓ should center content properly (1 ms)
    accessibility
      ✓ should be accessible with screen readers
      ✓ should have proper semantic structure (1 ms)
    integration with LoadingSpinner
      ✓ should pass correct props to LoadingSpinner

PASS src/__tests__/lib/utils.test.ts
  utils
    cn
      ✓ should merge class names correctly (1 ms)
      ✓ should handle conditional classes (1 ms)
      ✓ should merge tailwind classes correctly
    Date utilities
      formatDate
        ✓ should format date with default format (1 ms)
        ✓ should format date with custom format (1 ms)
        ✓ should handle Date object (1 ms)
      formatTime
        ✓ should format time correctly (1 ms)
        ✓ should handle Date object
      formatDateTime
        ✓ should format datetime correctly
      isToday
        ✓ should return true for today (1 ms)
        ✓ should return false for other dates
      isSameWeek
        ✓ should return true for dates in same week
        ✓ should return false for dates in different weeks
    String utilities
      truncate
        ✓ should truncate long strings
        ✓ should not truncate short strings
        ✓ should handle exact length
      capitalize
        ✓ should capitalize first letter
        ✓ should handle uppercase strings
        ✓ should handle mixed case
        ✓ should handle empty string
    Array utilities
      groupBy
        ✓ should group array by key
        ✓ should handle empty array
      sortBy
        ✓ should sort array by key ascending (1 ms)
        ✓ should sort array by key descending
        ✓ should default to ascending order
    Validation utilities
      isValidEmail
        ✓ should validate correct email
        ✓ should reject invalid email
      isValidPassword
        ✓ should validate password with minimum length
        ✓ should reject short passwords
    Schedule utilities
      getDayName
        ✓ should return correct day names (1 ms)
        ✓ should handle invalid day numbers
      getShiftTime
        ✓ should return correct shift times
        ✓ should handle invalid shift numbers
      getShiftSession
        ✓ should return correct shift sessions
        ✓ should handle invalid shift numbers
    Local storage utilities
      getFromStorage
        ✓ should return parsed value from localStorage
        ✓ should return default value when item not found
        ✓ should return default value on parse error (1 ms)
        ✓ should return default value in SSR environment
      setToStorage
        ✓ should set value to localStorage
        ✓ should handle errors gracefully
        ✓ should do nothing in SSR environment
      removeFromStorage
        ✓ should remove item from localStorage (1 ms)
        ✓ should handle errors gracefully
    Error handling utilities
      getErrorMessage
        ✓ should return error message from Error object
        ✓ should return string error as is
        ✓ should return default message for unknown error types
        ✓ should handle null and undefined

FAIL src/__tests__/mocks/providers.tsx
  ● Test suite failed to run

    Your test suite must contain at least one test.

      at onResult (node_modules/@jest/core/build/TestScheduler.js:133:18)
      at node_modules/@jest/core/build/TestScheduler.js:254:19
      at node_modules/emittery/index.js:363:13
          at Array.map (<anonymous>)
      at Emittery.emit (node_modules/emittery/index.js:361:23)

FAIL src/__tests__/mocks/server.ts
  ● Test suite failed to run

    Cannot find module 'msw/node' from 'src/__tests__/mocks/server.ts'

       9 |   server.listen({
      10 |     onUnhandledRequest: 'warn',
    > 11 |   })
         |     ^
      12 | })
      13 |
      14 | // Reset any request handlers that we may add during the tests,

      at Resolver._throwModNotFoundError (node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.<anonymous> (src/__tests__/mocks/server.ts:11:15)

FAIL src/__tests__/utils/test-utils.tsx
  ● Test suite failed to run

    Your test suite must contain at least one test.

      at onResult (node_modules/@jest/core/build/TestScheduler.js:133:18)
      at node_modules/@jest/core/build/TestScheduler.js:254:19
      at node_modules/emittery/index.js:363:13
          at Array.map (<anonymous>)
      at Emittery.emit (node_modules/emittery/index.js:361:23)

FAIL src/__tests__/mocks/handlers.ts
  ● Test suite failed to run

    Cannot find module 'msw' from 'src/__tests__/mocks/handlers.ts'

      20 |     if (username === 'student001' && password) {
      21 |       return HttpResponse.text(mockStorageData.signInToken)
    > 22 |     } else {
         |             ^
      23 |       return HttpResponse.text('Authentication failed', { status: 401 })
      24 |     }
      25 |   }),

      at Resolver._throwModNotFoundError (node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.<anonymous> (src/__tests__/mocks/handlers.ts:22:14)

FAIL src/__tests__/mocks/data.ts
  ● Test suite failed to run

    Your test suite must contain at least one test.

      at onResult (node_modules/@jest/core/build/TestScheduler.js:133:18)
      at node_modules/@jest/core/build/TestScheduler.js:254:19
      at node_modules/emittery/index.js:363:13
          at Array.map (<anonymous>)
      at Emittery.emit (node_modules/emittery/index.js:361:23)

---------------------------|---------|----------|---------|---------|---------------------------------------------------------------------------
File                       | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s                                                         
---------------------------|---------|----------|---------|---------|---------------------------------------------------------------------------
All files                  |   30.79 |    27.34 |   35.85 |   31.73 |                                                                           
 src                       |       0 |      100 |       0 |       0 |                                                                           
  middleware.ts            |       0 |      100 |       0 |       0 | 5-24                                                                      
 src/app                   |       0 |        0 |       0 |       0 |                                                                           
  layout.tsx               |       0 |      100 |       0 |       0 | 2-11                                                                      
  page.tsx                 |       0 |        0 |       0 |       0 | 3-17                                                                      
 src/app/(main)            |       0 |      100 |       0 |       0 |                                                                           
  layout.tsx               |       0 |      100 |       0 |       0 | 1-4                                                                       
 src/app/(main)/about      |       0 |      100 |       0 |       0 |                                                                           
  page.tsx                 |       0 |      100 |       0 |       0 | 1-6                                                                       
 src/app/(main)/calendar   |       0 |        0 |       0 |       0 |                                                                           
  page.tsx                 |       0 |        0 |       0 |       0 | 3-487                                                                     
 src/app/(main)/changelogs |       0 |        0 |       0 |       0 |                                                                           
  page.tsx                 |       0 |        0 |       0 |       0 | 1-173                                                                     
 src/app/login             |       0 |        0 |       0 |       0 |                                                                           
  page.tsx                 |       0 |        0 |       0 |       0 | 3-166                                                                     
 src/components            |       0 |      100 |       0 |       0 |                                                                           
  Header.tsx               |       0 |      100 |       0 |       0 | 1-4                                                                       
 src/components/layout     |       0 |        0 |       0 |       0 |                                                                           
  AppLayout.tsx            |       0 |        0 |       0 |       0 | 3-34                                                                      
  Footer.tsx               |       0 |      100 |       0 |       0 | 1                                                                         
  Header.tsx               |       0 |        0 |       0 |       0 | 3-115                                                                     
 src/components/ui         |    7.25 |    13.04 |   11.11 |    7.75 |                                                                           
  alert.tsx                |       0 |      100 |     100 |       0 | 1-49                                                                      
  badge.tsx                |       0 |      100 |       0 |       0 | 1-36                                                                      
  button.tsx               |      90 |      100 |     100 |     100 |                                                                           
  card.tsx                 |       0 |      100 |     100 |       0 | 1-56                                                                      
  dialog.tsx               |       0 |      100 |     100 |       0 | 3-121                                                                     
  empty-state.tsx          |       0 |        0 |       0 |       0 | 2-16                                                                      
  error-boundary.tsx       |       0 |        0 |       0 |       0 | 3-105                                                                     
  form.tsx                 |       0 |        0 |       0 |       0 | 1-175                                                                     
  input.tsx                |     100 |      100 |     100 |     100 |                                                                           
  label.tsx                |       0 |      100 |     100 |       0 | 1-24                                                                      
  lazy-image.tsx           |       0 |        0 |       0 |       0 | 3-53                                                                      
  loading-spinner.tsx      |     100 |      100 |     100 |     100 |                                                                           
  select.tsx               |       0 |        0 |     100 |       0 | 1-150                                                                     
  separator.tsx            |       0 |        0 |     100 |       0 | 1-29                                                                      
  skeleton.tsx             |       0 |      100 |       0 |       0 | 1-15                                                                      
  skip-to-content.tsx      |       0 |        0 |       0 |       0 | 3-10                                                                      
  table.tsx                |       0 |      100 |     100 |       0 | 1-91                                                                      
  textarea.tsx             |       0 |      100 |       0 |       0 | 1-23                                                                      
  toast.tsx                |       0 |      100 |       0 |       0 | 1-126                                                                     
  toaster.tsx              |       0 |        0 |       0 |       0 | 1-17                                                                      
 src/contexts              |      98 |    96.42 |     100 |      98 |                                                                           
  AppContext.tsx           |      98 |    96.42 |     100 |      98 | 147                                                                       
 src/hooks                 |   90.79 |    57.14 |   79.31 |   93.08 |                                                                           
  use-calendar-data.ts     |     100 |       60 |     100 |     100 | 38-80                                                                     
  use-notifications.ts     |     100 |      100 |     100 |     100 |                                                                           
  use-toast.ts             |   73.68 |    56.25 |   66.66 |   79.24 | 63,67-68,86-89,119-127,149,162                                            
 src/lib                   |   97.46 |    96.42 |     100 |   98.55 |                                                                           
  utils.ts                 |   97.46 |    96.42 |     100 |   98.55 | 60                                                                        
 src/lib/ts                |   22.34 |    22.13 |   15.38 |   23.64 |                                                                           
  calendar.ts              |    5.77 |        0 |       0 |    6.37 | 5-19,23-32,36-48,52-54,58-86,90-95,99-111,117-136,151-180,187-382,386-444 
  storage.ts               |     100 |      100 |     100 |     100 |                                                                           
  user.ts                  |     100 |     87.5 |     100 |     100 | 28                                                                        
  worker.ts                |       0 |      100 |       0 |       0 | 6-11                                                                      
 src/pages                 |       0 |        0 |       0 |       0 |                                                                           
  AboutPage.tsx            |       0 |      100 |       0 |       0 | 1-3                                                                       
  ChangelogsPage.tsx       |       0 |      100 |       0 |       0 | 1                                                                         
  LoginPage.tsx            |       0 |        0 |       0 |       0 | 1-213                                                                     
---------------------------|---------|----------|---------|---------|---------------------------------------------------------------------------
Jest: "global" coverage threshold for statements (70%) not met: 30.79%
Jest: "global" coverage threshold for branches (70%) not met: 27.34%
Jest: "global" coverage threshold for lines (70%) not met: 31.73%
Jest: "global" coverage threshold for functions (70%) not met: 35.85%
Test Suites: 7 failed, 10 passed, 17 total
Tests:       7 failed, 216 passed, 223 total
Snapshots:   0 total
Time:        3.356 s
Ran all test suites.

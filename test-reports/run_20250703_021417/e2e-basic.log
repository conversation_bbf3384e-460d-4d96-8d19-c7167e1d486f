[dotenv@17.0.1] injecting env (11) from .env – [tip] encrypt with dotenvx: https://dotenvx.com

Running 40 tests using 4 workers

[1A[2K[dotenv@17.0.1] injecting env (0) from .env – [tip] encrypt with dotenvx: https://dotenvx.com

[1A[2K[dotenv@17.0.1] injecting env (0) from .env – [tip] encrypt with dotenvx: https://dotenvx.com

[1A[2K[dotenv@17.0.1] injecting env (0) from .env – [tip] encrypt with dotenvx: https://dotenvx.com

[1A[2K[dotenv@17.0.1] injecting env (0) from .env – [tip] encrypt with dotenvx: https://dotenvx.com

[1A[2K[1/40] [chromium] › e2e/homepage.spec.ts:26:6 › Homepage › should be responsive on mobile
[1A[2K[2/40] [chromium] › e2e/homepage.spec.ts:4:6 › Homepage › should load the homepage successfully
[1A[2K[3/40] [chromium] › e2e/homepage.spec.ts:39:6 › Homepage › should handle dark/light theme toggle
[1A[2K[4/40] [chromium] › e2e/homepage.spec.ts:14:6 › Homepage › should have proper meta tags
[1A[2K  1) [chromium] › e2e/homepage.spec.ts:4:6 › Homepage › should load the homepage successfully ──────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/
    Call log:
    [2m  - navigating to "http://localhost:3001/", waiting until "load"[22m


      3 | test.describe('Homepage', () => {
      4 | 	test('should load the homepage successfully', async ({ page }) => {
    > 5 | 		await page.goto('/');
        | 		           ^
      6 |
      7 | 		// Check if the page loads without errors - adjust title to match actual app
      8 | 		await expect(page).toHaveTitle(/KMA Schedule/i);
        at /Users/<USER>/Github/kma-schedule-ngosangns/e2e/homepage.spec.ts:5:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-load-the-homepage-successfully-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-load-the-homepage-successfully-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────


[1A[2K  2) [chromium] › e2e/homepage.spec.ts:14:6 › Homepage › should have proper meta tags ──────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/
    Call log:
    [2m  - navigating to "http://localhost:3001/", waiting until "load"[22m


      13 |
      14 | 	test('should have proper meta tags', async ({ page }) => {
    > 15 | 		await page.goto('/');
         | 		           ^
      16 |
      17 | 		// Check for viewport meta tag (use first() to handle multiple viewport tags)
      18 | 		const viewport = page.locator('meta[name="viewport"]').first();
        at /Users/<USER>/Github/kma-schedule-ngosangns/e2e/homepage.spec.ts:15:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-have-proper-meta-tags-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-have-proper-meta-tags-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────


[1A[2K  3) [chromium] › e2e/homepage.spec.ts:26:6 › Homepage › should be responsive on mobile ────────────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/
    Call log:
    [2m  - navigating to "http://localhost:3001/", waiting until "load"[22m


      27 | 		// Set mobile viewport
      28 | 		await page.setViewportSize({ width: 375, height: 667 });
    > 29 | 		await page.goto('/');
         | 		           ^
      30 |
      31 | 		// Check if page is still functional on mobile
      32 | 		await expect(page.locator('body')).toBeVisible();
        at /Users/<USER>/Github/kma-schedule-ngosangns/e2e/homepage.spec.ts:29:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-be-responsive-on-mobile-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-be-responsive-on-mobile-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────


[1A[2K  4) [chromium] › e2e/homepage.spec.ts:39:6 › Homepage › should handle dark/light theme toggle ─────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/
    Call log:
    [2m  - navigating to "http://localhost:3001/", waiting until "load"[22m


      38 |
      39 | 	test('should handle dark/light theme toggle', async ({ page }) => {
    > 40 | 		await page.goto('/');
         | 		           ^
      41 |
      42 | 		// Look for theme toggle button (if exists)
      43 | 		const themeToggle = page.locator('[data-testid="theme-toggle"]').first();
        at /Users/<USER>/Github/kma-schedule-ngosangns/e2e/homepage.spec.ts:40:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-handle-dark-light-theme-toggle-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-handle-dark-light-theme-toggle-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────


[1A[2K[dotenv@17.0.1] injecting env (0) from .env – [tip] encrypt with dotenvx: https://dotenvx.com

[1A[2K[dotenv@17.0.1] injecting env (0) from .env – [tip] encrypt with dotenvx: https://dotenvx.com

[1A[2K[5/40] [chromium] › e2e/homepage.spec.ts:128:6 › Homepage › should handle network failures gracefully
[1A[2K[6/40] [chromium] › e2e/homepage.spec.ts:75:6 › Homepage › should load without JavaScript errors
[1A[2K[dotenv@17.0.1] injecting env (0) from .env – [tip] encrypt with dotenvx: https://dotenvx.com

[1A[2K[dotenv@17.0.1] injecting env (0) from .env – [tip] encrypt with dotenvx: https://dotenvx.com

[1A[2K[7/40] [chromium] › e2e/homepage.spec.ts:59:6 › Homepage › should have proper accessibility features
[1A[2K[8/40] [chromium] › e2e/homepage.spec.ts:108:6 › Homepage › should have proper performance characteristics
[1A[2K  5) [chromium] › e2e/homepage.spec.ts:59:6 › Homepage › should have proper accessibility features ─

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/
    Call log:
    [2m  - navigating to "http://localhost:3001/", waiting until "load"[22m


      58 |
      59 | 	test('should have proper accessibility features', async ({ page }) => {
    > 60 | 		await page.goto('/');
         | 		           ^
      61 |
      62 | 		// Check for proper heading structure
      63 | 		const h1 = page.locator('h1').first();
        at /Users/<USER>/Github/kma-schedule-ngosangns/e2e/homepage.spec.ts:60:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-have-proper-accessibility-features-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-have-proper-accessibility-features-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────


[1A[2K  6) [chromium] › e2e/homepage.spec.ts:75:6 › Homepage › should load without JavaScript errors ─────

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/
    Call log:
    [2m  - navigating to "http://localhost:3001/", waiting until "load"[22m


      88 | 		});
      89 |
    > 90 | 		await page.goto('/');
         | 		           ^
      91 |
      92 | 		// Wait a bit for any async operations
      93 | 		await page.waitForTimeout(2000);
        at /Users/<USER>/Github/kma-schedule-ngosangns/e2e/homepage.spec.ts:90:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-load-without-JavaScript-errors-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-load-without-JavaScript-errors-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────


[1A[2K  7) [chromium] › e2e/homepage.spec.ts:108:6 › Homepage › should have proper performance characteristics 

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/
    Call log:
    [2m  - navigating to "http://localhost:3001/", waiting until "load"[22m


      107 |
      108 | 	test('should have proper performance characteristics', async ({ page }) => {
    > 109 | 		await page.goto('/');
          | 		           ^
      110 |
      111 | 		// Wait for page to be fully loaded
      112 | 		await page.waitForLoadState('networkidle');
        at /Users/<USER>/Github/kma-schedule-ngosangns/e2e/homepage.spec.ts:109:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-h-eec81-performance-characteristics-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-h-eec81-performance-characteristics-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────


[1A[2K  8) [chromium] › e2e/homepage.spec.ts:128:6 › Homepage › should handle network failures gracefully 

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/
    Call log:
    [2m  - navigating to "http://localhost:3001/", waiting until "load"[22m


      128 | 	test('should handle network failures gracefully', async ({ page }) => {
      129 | 		// First load the page normally
    > 130 | 		await page.goto('/');
          | 		           ^
      131 |
      132 | 		// Then simulate offline condition
      133 | 		await page.context().setOffline(true);
        at /Users/<USER>/Github/kma-schedule-ngosangns/e2e/homepage.spec.ts:130:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-handle-network-failures-gracefully-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-handle-network-failures-gracefully-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────


[1A[2K[dotenv@17.0.1] injecting env (0) from .env – [tip] encrypt with dotenvx: https://dotenvx.com

[1A[2K[dotenv@17.0.1] injecting env (0) from .env – [tip] encrypt with dotenvx: https://dotenvx.com

[1A[2K[9/40] [firefox] › e2e/homepage.spec.ts:4:6 › Homepage › should load the homepage successfully
[1A[2K[10/40] [firefox] › e2e/homepage.spec.ts:14:6 › Homepage › should have proper meta tags
[1A[2K[dotenv@17.0.1] injecting env (0) from .env – [tip] encrypt with dotenvx: https://dotenvx.com

[1A[2K[11/40] [firefox] › e2e/homepage.spec.ts:39:6 › Homepage › should handle dark/light theme toggle
[1A[2K[dotenv@17.0.1] injecting env (0) from .env – [tip] encrypt with dotenvx: https://dotenvx.com

[1A[2K[12/40] [firefox] › e2e/homepage.spec.ts:26:6 › Homepage › should be responsive on mobile
[1A[2K  9) [firefox] › e2e/homepage.spec.ts:4:6 › Homepage › should load the homepage successfully ───────

    Error: page.goto: NS_ERROR_CONNECTION_REFUSED
    Call log:
    [2m  - navigating to "http://localhost:3001/", waiting until "load"[22m


      3 | test.describe('Homepage', () => {
      4 | 	test('should load the homepage successfully', async ({ page }) => {
    > 5 | 		await page.goto('/');
        | 		           ^
      6 |
      7 | 		// Check if the page loads without errors - adjust title to match actual app
      8 | 		await expect(page).toHaveTitle(/KMA Schedule/i);
        at /Users/<USER>/Github/kma-schedule-ngosangns/e2e/homepage.spec.ts:5:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-load-the-homepage-successfully-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-load-the-homepage-successfully-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-load-the-homepage-successfully-firefox/error-context.md


[1A[2K[dotenv@17.0.1] injecting env (0) from .env – [tip] encrypt with dotenvx: https://dotenvx.com

[1A[2K[13/40] [firefox] › e2e/homepage.spec.ts:59:6 › Homepage › should have proper accessibility features
[1A[2K  10) [firefox] › e2e/homepage.spec.ts:39:6 › Homepage › should handle dark/light theme toggle ─────

    Error: page.goto: NS_ERROR_CONNECTION_REFUSED
    Call log:
    [2m  - navigating to "http://localhost:3001/", waiting until "load"[22m


      38 |
      39 | 	test('should handle dark/light theme toggle', async ({ page }) => {
    > 40 | 		await page.goto('/');
         | 		           ^
      41 |
      42 | 		// Look for theme toggle button (if exists)
      43 | 		const themeToggle = page.locator('[data-testid="theme-toggle"]').first();
        at /Users/<USER>/Github/kma-schedule-ngosangns/e2e/homepage.spec.ts:40:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-handle-dark-light-theme-toggle-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-handle-dark-light-theme-toggle-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-handle-dark-light-theme-toggle-firefox/error-context.md


[1A[2K[dotenv@17.0.1] injecting env (0) from .env – [tip] encrypt with dotenvx: https://dotenvx.com

[1A[2K[14/40] [firefox] › e2e/homepage.spec.ts:75:6 › Homepage › should load without JavaScript errors
[1A[2K  11) [firefox] › e2e/homepage.spec.ts:26:6 › Homepage › should be responsive on mobile ────────────

    Error: page.goto: NS_ERROR_CONNECTION_REFUSED
    Call log:
    [2m  - navigating to "http://localhost:3001/", waiting until "load"[22m


      27 | 		// Set mobile viewport
      28 | 		await page.setViewportSize({ width: 375, height: 667 });
    > 29 | 		await page.goto('/');
         | 		           ^
      30 |
      31 | 		// Check if page is still functional on mobile
      32 | 		await expect(page.locator('body')).toBeVisible();
        at /Users/<USER>/Github/kma-schedule-ngosangns/e2e/homepage.spec.ts:29:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-be-responsive-on-mobile-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-be-responsive-on-mobile-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-be-responsive-on-mobile-firefox/error-context.md


[1A[2K[dotenv@17.0.1] injecting env (0) from .env – [tip] encrypt with dotenvx: https://dotenvx.com

[1A[2K[15/40] [firefox] › e2e/homepage.spec.ts:108:6 › Homepage › should have proper performance characteristics
[1A[2K  12) [firefox] › e2e/homepage.spec.ts:14:6 › Homepage › should have proper meta tags ──────────────

    Error: page.goto: NS_ERROR_CONNECTION_REFUSED
    Call log:
    [2m  - navigating to "http://localhost:3001/", waiting until "load"[22m


      13 |
      14 | 	test('should have proper meta tags', async ({ page }) => {
    > 15 | 		await page.goto('/');
         | 		           ^
      16 |
      17 | 		// Check for viewport meta tag (use first() to handle multiple viewport tags)
      18 | 		const viewport = page.locator('meta[name="viewport"]').first();
        at /Users/<USER>/Github/kma-schedule-ngosangns/e2e/homepage.spec.ts:15:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-have-proper-meta-tags-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-have-proper-meta-tags-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: test-reports/run_20250703_021417/playwright-basic/homepage-Homepage-should-have-proper-meta-tags-firefox/error-context.md


[1A[2K  13) [firefox] › e2e/homepage.spec.ts:75:6 › Homepage › should load without JavaScript errors ─────

    Test was interrupted.


[1A[2K  14) [firefox] › e2e/homepage.spec.ts:108:6 › Homepage › should have proper performance characteristics 

    Test was interrupted.


[1A[2K  15) [firefox] › e2e/homepage.spec.ts:59:6 › Homepage › should have proper accessibility features ─

    Test was interrupted.

    Error: browserContext.newPage: Test ended.


[1A[2K  12 failed
    [chromium] › e2e/homepage.spec.ts:4:6 › Homepage › should load the homepage successfully ───────
    [chromium] › e2e/homepage.spec.ts:14:6 › Homepage › should have proper meta tags ───────────────
    [chromium] › e2e/homepage.spec.ts:26:6 › Homepage › should be responsive on mobile ─────────────
    [chromium] › e2e/homepage.spec.ts:39:6 › Homepage › should handle dark/light theme toggle ──────
    [chromium] › e2e/homepage.spec.ts:59:6 › Homepage › should have proper accessibility features ──
    [chromium] › e2e/homepage.spec.ts:75:6 › Homepage › should load without JavaScript errors ──────
    [chromium] › e2e/homepage.spec.ts:108:6 › Homepage › should have proper performance characteristics 
    [chromium] › e2e/homepage.spec.ts:128:6 › Homepage › should handle network failures gracefully ─
    [firefox] › e2e/homepage.spec.ts:4:6 › Homepage › should load the homepage successfully ────────
    [firefox] › e2e/homepage.spec.ts:14:6 › Homepage › should have proper meta tags ────────────────
    [firefox] › e2e/homepage.spec.ts:26:6 › Homepage › should be responsive on mobile ──────────────
    [firefox] › e2e/homepage.spec.ts:39:6 › Homepage › should handle dark/light theme toggle ───────
  3 interrupted
    [firefox] › e2e/homepage.spec.ts:59:6 › Homepage › should have proper accessibility features ───
    [firefox] › e2e/homepage.spec.ts:75:6 › Homepage › should load without JavaScript errors ───────
    [firefox] › e2e/homepage.spec.ts:108:6 › Homepage › should have proper performance characteristics 
  25 did not run

[36m  Serving HTML report at http://localhost:9323. Press Ctrl+C to quit.[39m

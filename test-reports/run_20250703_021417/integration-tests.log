
> kma-schedule-ngosangns@0.0.1 test
> jest --testPathPattern=integration --ci --watchAll=false

PASS src/__tests__/integration/login-flow.test.tsx
  Login Flow Integration
    ✓ should handle successful login flow (103 ms)
    ✓ should handle failed login flow (59 ms)
    ✓ should disable login button during processing (2 ms)
    ✓ should handle empty credentials (11 ms)
    ✓ should handle network errors during login (57 ms)
    ✓ should clear form after successful login (52 ms)
    ✓ should maintain form state during failed login (141 ms)

PASS src/__tests__/integration/calendar-flow.test.tsx
  Calendar Flow Integration
    ✓ should display login prompt when not authenticated (2 ms)
    ✓ should display calendar data when authenticated (10 ms)
    ✓ should handle semester change (39 ms)
    ✓ should handle calendar export (9 ms)
    ✓ should handle logout (9 ms)
    ✓ should display no calendar message when no data available (1 ms)
    ✓ should disable export when no calendar or student data (10 ms)
    ✓ should maintain UI state during semester change (25 ms)

Test Suites: 2 passed, 2 total
Tests:       15 passed, 15 total
Snapshots:   0 total
Time:        1.056 s
Ran all test suites matching /integration/i.

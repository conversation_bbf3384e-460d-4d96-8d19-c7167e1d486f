npm warn Unknown env config "version-git-tag". This will stop working in the next major version of npm.
npm warn Unknown env config "argv". This will stop working in the next major version of npm.
npm warn Unknown env config "version-commit-hooks". This will stop working in the next major version of npm.
npm warn Unknown env config "version-git-message". This will stop working in the next major version of npm.
npm warn Unknown env config "version-tag-prefix". This will stop working in the next major version of npm.

> kma-schedule-ngosangns@0.0.1 test
> jest --testPathPattern=integration --ci --watchAll=false

PASS src/__tests__/integration/calendar-flow.test.tsx
  Calendar Flow Integration
    ✓ should display login prompt when not authenticated (16 ms)
    ✓ should display calendar data when authenticated (10 ms)
    ✓ should handle semester change (66 ms)
    ✓ should handle calendar export (12 ms)
    ✓ should handle logout (11 ms)
    ✓ should display no calendar message when no data available (2 ms)
    ✓ should disable export when no calendar or student data (11 ms)
    ✓ should maintain UI state during semester change (23 ms)

PASS src/__tests__/integration/login-flow.test.tsx
  Login Flow Integration
    ✓ should handle successful login flow (122 ms)
    ✓ should handle failed login flow (58 ms)
    ✓ should disable login button during processing (2 ms)
    ✓ should handle empty credentials (10 ms)
    ✓ should handle network errors during login (53 ms)
    ✓ should clear form after successful login (52 ms)
    ✓ should maintain form state during failed login (55 ms)

Test Suites: 2 passed, 2 total
Tests:       15 passed, 15 total
Snapshots:   0 total
Time:        0.957 s, estimated 2 s
Ran all test suites matching /integration/i.

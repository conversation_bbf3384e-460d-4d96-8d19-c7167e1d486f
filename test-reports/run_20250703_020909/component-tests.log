npm warn Unknown env config "version-git-tag". This will stop working in the next major version of npm.
npm warn Unknown env config "argv". This will stop working in the next major version of npm.
npm warn Unknown env config "version-commit-hooks". This will stop working in the next major version of npm.
npm warn Unknown env config "version-git-message". This will stop working in the next major version of npm.
npm warn Unknown env config "version-tag-prefix". This will stop working in the next major version of npm.

> kma-schedule-ngosangns@0.0.1 test
> jest --testPathPattern=components --ci --watchAll=false

PASS src/__tests__/components/ui/input.test.tsx
  Input
    ✓ should render with default props (35 ms)
    ✓ should render with placeholder (4 ms)
    ✓ should handle text input (40 ms)
    ✓ should handle onChange events (20 ms)
    ✓ should be disabled when disabled prop is true (4 ms)
    ✓ should not accept input when disabled (5 ms)
    ✓ should handle different input types (7 ms)
    ✓ should merge custom className with default classes (4 ms)
    ✓ should forward ref correctly (2 ms)
    ✓ should pass through HTML input attributes (1 ms)
    ✓ should handle focus and blur events (20 ms)
    ✓ should handle keyboard events (3 ms)
    ✓ should support controlled input (28 ms)
    ✓ should support uncontrolled input with defaultValue (2 ms)
    ✓ should have correct accessibility attributes (3 ms)
    ✓ should support form validation attributes (2 ms)
    ✓ should handle file input type (1 ms)
    ✓ should support autoComplete (1 ms)
    ✓ should have correct display name
    focus management
      ✓ should be focusable (7 ms)
      ✓ should show focus styles (9 ms)
    placeholder styling
      ✓ should apply placeholder styles (1 ms)

PASS src/__tests__/components/ui/button.test.tsx
  Button
    ✓ should render with default props (14 ms)
    ✓ should render children correctly (1 ms)
    ✓ should handle click events (9 ms)
    ✓ should be disabled when disabled prop is true (3 ms)
    ✓ should not call onClick when disabled (9 ms)
    ✓ should merge custom className with default classes (3 ms)
    ✓ should forward ref correctly
    ✓ should pass through HTML button attributes (1 ms)
    ✓ should handle keyboard events (3 ms)
    ✓ should have correct accessibility attributes (3 ms)
    ✓ should support focus management (9 ms)
    ✓ should have correct display name
    variants
      ✓ should apply default variant classes (2 ms)
      ✓ should apply destructive variant classes (2 ms)
      ✓ should apply outline variant classes (3 ms)
      ✓ should apply secondary variant classes (2 ms)
      ✓ should apply ghost variant classes (2 ms)
      ✓ should apply link variant classes (3 ms)
    sizes
      ✓ should apply default size classes (4 ms)
      ✓ should apply small size classes (3 ms)
      ✓ should apply large size classes (3 ms)
      ✓ should apply icon size classes (3 ms)
    asChild prop
      ✓ should render as child component when asChild is true (4 ms)
      ✓ should render as button when asChild is false (4 ms)
    combinations
      ✓ should apply multiple props correctly (3 ms)

PASS src/__tests__/components/ui/loading-spinner.test.tsx
  LoadingSpinner
    ✓ should render with default props (3 ms)
    ✓ should render with text (1 ms)
    ✓ should not render text when not provided (2 ms)
    ✓ should merge custom className with default classes (1 ms)
    ✓ should render with both size and text (1 ms)
    ✓ should have correct structure (1 ms)
    ✓ should apply animation class
    sizes
      ✓ should apply small size classes (1 ms)
      ✓ should apply medium size classes (default)
      ✓ should apply large size classes (1 ms)
      ✓ should default to medium size when size not specified
  PageLoader
    ✓ should render with default text (1 ms)
    ✓ should render with custom text
    ✓ should have correct container styling (2 ms)
    ✓ should use large size spinner
    ✓ should contain LoadingSpinner component (1 ms)
    ✓ should center content properly (1 ms)
    accessibility
      ✓ should be accessible with screen readers (1 ms)
      ✓ should have proper semantic structure (1 ms)
    integration with LoadingSpinner
      ✓ should pass correct props to LoadingSpinner (1 ms)

Test Suites: 3 passed, 3 total
Tests:       67 passed, 67 total
Snapshots:   0 total
Time:        0.856 s, estimated 1 s
Ran all test suites matching /components/i.

npm warn Unknown env config "version-git-tag". This will stop working in the next major version of npm.
npm warn Unknown env config "argv". This will stop working in the next major version of npm.
npm warn Unknown env config "version-commit-hooks". This will stop working in the next major version of npm.
npm warn Unknown env config "version-git-message". This will stop working in the next major version of npm.
npm warn Unknown env config "version-tag-prefix". This will stop working in the next major version of npm.
[dotenv@17.0.1] injecting env (11) from .env – [tip] encrypt with dotenvx: https://dotenvx.com
[1A[2K[2m[WebServer] [22mnpm warn Unknown env config "version-git-tag". This will stop working in the next major version of npm.

[1A[2K[2m[WebServer] [22mnpm warn Unknown env config "argv". This will stop working in the next major version of npm.
[2m[WebServer] [22mnpm warn Unknown env config "version-commit-hooks". This will stop working in the next major version of npm.
[2m[WebServer] [22mnpm warn Unknown env config "version-git-message". This will stop working in the next major version of npm.
[2m[WebServer] [22mnpm warn Unknown env config "version-tag-prefix". This will stop working in the next major version of npm.

